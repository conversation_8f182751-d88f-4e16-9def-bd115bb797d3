<template>
  <!-- 主横幅 -->
  <header class="hero-section" id="hero" role="banner">
    <div class="container h-100">
      <div class="row h-100 align-items-center">
        <div class="col-md-6">
          <h1 class="fw-bold fade-in">专业印刷服务，<br>品质成就未来</h1>
          <p class="lead slide-up">广州市晨兴印刷有限公司专注于高品质画册、包装盒、纸袋等印刷产品</p>
          <div class="mt-4">
            <router-link to="/products" class="btn btn-primary btn-gradient me-2" aria-label="浏览我们的产品">
              <i class="bi bi-arrow-right me-2" aria-hidden="true"></i>浏览产品
            </router-link>
            <router-link to="/contact" class="btn btn-outline-primary" aria-label="联系我们获取报价">
              <i class="bi bi-telephone me-2" aria-hidden="true"></i>联系我们
            </router-link>
          </div>
        </div>
        <div class="col-md-6 d-none d-md-block">
          <carousel :items="carouselItems" id="headerCarousel" />
        </div>
      </div>
    </div>
  </header>

  <!-- 我们的服务 -->
  <section class="py-5 bg-light">
    <div class="container">
      <div class="text-center mb-5">
        <h2 class="fw-bold">我们的服务</h2>
        <p class="text-muted">为您提供全方位的印刷解决方案</p>
      </div>
      <div class="row g-4">
        <div v-for="(service, index) in services" :key="index" class="col-md-4">
          <service-card :service="service" />
        </div>
      </div>
    </div>
  </section>

  <!-- 产品展示 -->
  <section class="py-5">
    <div class="container">
      <div class="text-center mb-5">
        <h2 class="fw-bold">精选产品</h2>
        <p class="text-muted">查看我们的优质印刷产品</p>
      </div>
      <div class="row g-4">
        <div v-for="(product, index) in featuredProducts" :key="index" class="col-md-4">
          <product-card :product="product" />
        </div>
      </div>
    </div>
  </section>

  <!-- 为什么选择我们 -->
  <section class="py-5 bg-light">
    <div class="container">
      <div class="text-center mb-5">
        <h2 class="fw-bold">为什么选择晨兴印刷</h2>
        <p class="text-muted">我们的优势与承诺</p>
      </div>
      <div class="row g-4">
        <div v-for="(feature, index) in features" :key="index" class="col-md-3">
          <div class="text-center">
            <div class="icon-box mb-3">
              <i :class="`bi bi-${feature.icon} text-primary fs-1`"></i>
            </div>
            <h5>{{ feature.title }}</h5>
            <p class="text-muted small">{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 客户评价 -->
  <testimonials-section :testimonials="testimonials" />

  <!-- 联系我们 -->
  <contact-form-section />
</template>

<script setup>
import { ref } from 'vue'
import Carousel from '../components/Carousel.vue'
import ServiceCard from '../components/ServiceCard.vue'
import ProductCard from '../components/ProductCard.vue'
import TestimonialsSection from '../components/TestimonialsSection.vue'
import ContactFormSection from '../components/ContactFormSection.vue'

// 轮播图数据
const carouselItems = [
  { 
    id: 1, 
    image: 'images/轮播图/印刷机特写.jpg', 
    alt: '专业印刷机特写，展示我们的生产设备',
    label: '1/3 - 印刷机特写'
  },
  { 
    id: 2, 
    image: 'images/轮播图/纸卡盒.jpg', 
    alt: '精美纸卡盒产品展示',
    label: '2/3 - 纸卡盒'
  },
  { 
    id: 3, 
    image: 'images/轮播图/礼品书型盒.jpg', 
    alt: '高档礼品书型盒包装',
    label: '3/3 - 礼品书型盒'
  }
]

// 服务数据
const services = [
  {
    icon: 'book',
    title: '画册印刷',
    description: '高品质企业画册、产品目录、宣传册等，展现您的品牌形象'
  },
  {
    icon: 'box',
    title: '包装盒制作',
    description: '精美产品包装盒设计与制作，提升产品价值与品牌形象'
  },
  {
    icon: 'bag',
    title: '纸袋制作',
    description: '环保时尚的纸袋设计与制作，满足各类商业与礼品需求'
  }
]

// 产品数据
const featuredProducts = [
  {
    image: 'images/轮播图/纸卡盒.jpg',
    title: '高档企业画册',
    description: '精装硬壳，内页采用进口铜版纸，UV工艺',
    link: '/products'
  },
  {
    image: 'images/轮播图/礼品书型盒.jpg',
    title: '高端礼品包装盒',
    description: '特种纸材质，烫金工艺，磁铁扣设计',
    link: '/products'
  },
  {
    image: 'images/轮播图/纸袋.jpg',
    title: '定制环保纸袋',
    description: '环保材质，个性设计，完美展现品牌形象',
    link: '/products'
  }
]

// 特点数据
const features = [
  {
    icon: 'award',
    title: '专业品质',
    description: '15年印刷经验，严格的质量控制'
  },
  {
    icon: 'lightning',
    title: '快速交付',
    description: '高效生产流程，准时交付'
  },
  {
    icon: 'palette',
    title: '创新设计',
    description: '专业设计团队，创新包装解决方案'
  },
  {
    icon: 'shield-check',
    title: '品质保证',
    description: '严格的质量控制，确保每件产品完美'
  }
]

// 客户评价数据
const testimonials = [
  {
    rating: 4,
    text: '"晨兴印刷为我们公司制作的产品目录质量非常出色，印刷精美，色彩还原度高，客户反馈非常好。"',
    name: '张经理',
    company: '广州某电子公司',
    avatarColor: 'primary'
  },
  {
    rating: 4.5,
    text: '"包装盒设计新颖，制作精细，交货及时，服务态度非常好，值得推荐！"',
    name: '李总监',
    company: '深圳某贸易公司',
    avatarColor: 'success'
  },
  {
    rating: 5,
    text: '"纸袋定制效果非常棒，印刷清晰，材质厚实，客户很满意。"',
    name: '王经理',
    company: '东莞某化妆品公司',
    avatarColor: 'info'
  }
]
</script>