/* 全局样式 */
:root {
    --primary-color: #0056b3;
    --primary-dark: #003d7a;
    --primary-light: #e7f3ff;
    --secondary-color: #6c757d;
    --accent-color: #ffc107;
    --success-color: #28a745;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --gradient-primary: linear-gradient(135deg, #0056b3, #007bff);
    --gradient-hero: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
    --shadow-sm: 0 2px 8px rgba(0,0,0,0.08);
    --shadow-md: 0 4px 16px rgba(0,0,0,0.12);
    --shadow-lg: 0 8px 32px rgba(0,0,0,0.16);
    --border-radius: 1rem;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Helvetica Neue', <PERSON><PERSON>, 'PingFang SC', 'Hiragino Sans GB', 'Heiti SC', 'WenQuanYi Micro Hei', sans-serif;
    background: #f8f9fa;
    color: #222;
    line-height: 1.7;
    overflow-x: hidden;
    scroll-behavior: smooth;
}

/* 加载动画 */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loader-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--primary-light);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 平滑进入动画 */
.fade-in {
    animation: fadeIn 0.8s ease-out;
}

.slide-up {
    animation: slideUp 0.8s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(30px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-dark);
    text-decoration: none;
}

/* 按钮样式增强 */
.btn {
    border-radius: var(--border-radius);
    padding: 0.75rem 2rem;
    font-weight: 500;
    font-size: 0.95rem;
    letter-spacing: 0.5px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    background: var(--gradient-primary);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 动态渐变按钮 */
.btn-gradient {
    background: var(--gradient-primary);
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background: var(--gradient-primary) !important;
}

/* 增强导航栏样式 */
.navbar {
    background: rgba(255,255,255,0.95) !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: var(--shadow-sm);
    padding: 1rem 0;
    transition: var(--transition);
    position: relative;
}

.navbar.scrolled {
    background: rgba(255,255,255,0.98) !important;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    box-shadow: var(--shadow-md);
    padding: 0.5rem 0;
}

.navbar-brand {
    font-size: 1.75rem;
    font-weight: 700;
    position: relative;
}

.navbar-brand span {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.navbar-brand::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 3px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.navbar-brand:hover::after {
    width: 100%;
}

.navbar-nav .nav-link {
    padding: 0.5rem 1.2rem;
    font-weight: 500;
    position: relative;
    margin: 0 0.2rem;
    border-radius: 0.5rem;
    transition: var(--transition);
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::before,
.navbar-nav .nav-link.active::before {
    width: 80%;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color);
    background: var(--primary-light);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    color: var(--primary-color);
    font-weight: 600;
    background: var(--primary-light);
}

/* 移动端导航增强 */
.navbar-toggler {
    border: none;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: var(--transition);
}

.navbar-toggler:focus {
    box-shadow: none;
}

.navbar-toggler:hover {
    background: var(--primary-light);
}

@media (max-width: 991px) {
    .navbar-collapse {
        background: rgba(255,255,255,0.98);
        backdrop-filter: blur(15px);
        border-radius: var(--border-radius);
        margin-top: 1rem;
        padding: 1rem;
        box-shadow: var(--shadow-md);
    }
    
    .navbar-nav .nav-link {
        margin: 0.2rem 0;
    }
}

/* 增强主横幅样式 */
.hero-section {
    background: var(--gradient-hero);
    padding: 120px 0 80px 0;
    min-height: 600px;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.hero-section h1 {
    color: #222;
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    background: linear-gradient(135deg, #222, var(--primary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: slideUp 1s ease-out;
}

.hero-section .lead {
    font-size: 1.25rem;
    color: #555;
    margin-bottom: 2rem;
    animation: slideUp 1s ease-out 0.2s both;
}

.hero-section .btn {
    margin-right: 1rem;
    margin-bottom: 1rem;
    animation: slideUp 1s ease-out 0.4s both;
}

/* 轮播图增强 */
.carousel {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    animation: slideUp 1s ease-out 0.6s both;
}

.carousel-inner {
    border-radius: var(--border-radius);
}

.carousel-item img {
    height: 400px;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.carousel-item.active img {
    transform: scale(1.05);
}

.carousel-control-prev,
.carousel-control-next {
    width: 50px;
    height: 50px;
    background: rgba(0,0,0,0.5);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    backdrop-filter: blur(10px);
    transition: var(--transition);
}

.carousel-control-prev {
    left: 20px;
}

.carousel-control-next {
    right: 20px;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    background: rgba(0,0,0,0.7);
    transform: translateY(-50%) scale(1.1);
}

.carousel-indicators {
    bottom: 20px;
}

.carousel-indicators [data-bs-target] {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 5px;
    background: rgba(255,255,255,0.5);
    transition: var(--transition);
}

.carousel-indicators .active {
    background: white;
    transform: scale(1.2);
}

/* 增强卡片样式 */
.card {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    background: white;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-lg);
}

.card:hover::before {
    transform: scaleX(1);
}

.card-body {
    padding: 2rem;
}

/* 图标盒子增强 */
.icon-box {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-light);
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    font-size: 2.5rem;
    color: var(--primary-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.icon-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 50%;
}

.card:hover .icon-box::before {
    opacity: 1;
}

.card:hover .icon-box i {
    color: white;
    transform: scale(1.1);
    position: relative;
    z-index: 1;
}

/* 产品卡片增强 */
.product-card {
    position: relative;
    overflow: hidden;
}

.product-card img {
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
    height: 280px;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.product-card:hover img {
    transform: scale(1.1);
}

.product-card .card-body {
    position: relative;
    z-index: 2;
}

.product-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, var(--primary-color), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.product-card:hover::after {
    opacity: 0.1;
}

/* 服务卡片特殊效果 */
.service-card {
    position: relative;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
}

.service-card:hover {
    background: linear-gradient(135deg, #fff 0%, var(--primary-light) 100%);
}

/* 客户评价增强样式 */
.testimonial-card {
    position: relative;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-left: 4px solid var(--primary-color);
    transition: var(--transition);
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 6rem;
    color: var(--primary-light);
    font-family: Georgia, serif;
    line-height: 1;
    z-index: 1;
}

.testimonial-card:hover {
    border-left-color: var(--accent-color);
    background: linear-gradient(135deg, #fff 0%, var(--primary-light) 100%);
}

.testimonial-card .card-body {
    position: relative;
    z-index: 2;
}

/* 星级评分增强 */
.star-rating {
    display: flex;
    gap: 2px;
    margin-bottom: 1rem;
}

.star-rating i {
    font-size: 1.1rem;
    transition: var(--transition);
}

.testimonial-card:hover .star-rating i {
    transform: scale(1.1);
    animation: starTwinkle 0.6s ease;
}

@keyframes starTwinkle {
    0%, 100% { transform: scale(1.1); }
    50% { transform: scale(1.2); }
}

/* 客户头像增强 */
.client-avatar {
    border: 3px solid var(--primary-light);
    transition: var(--transition);
}

.testimonial-card:hover .client-avatar {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

/* 联系表单增强样式 */
.form-control, .form-select {
    border-radius: 0.75rem;
    padding: 1rem 1.25rem;
    border: 2px solid #e9ecef;
    background: #fff;
    transition: var(--transition);
    font-size: 0.95rem;
}

.form-control:focus, .form-select:focus {
    box-shadow: 0 0 0 4px rgba(0, 86, 179, 0.1);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.form-control::placeholder {
    color: #999;
    font-weight: 400;
}

/* 表单组增强 */
.form-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-floating {
    position: relative;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* 联系卡片增强 */
.contact-card {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: var(--border-radius);
    padding: 2.5rem;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.contact-info-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
    transition: var(--transition);
}

.contact-info-item:last-child {
    border-bottom: none;
}

.contact-info-item:hover {
    background: var(--primary-light);
    border-radius: 0.5rem;
    padding-left: 1rem;
    transform: translateX(5px);
}

.contact-info-item i {
    width: 40px;
    height: 40px;
    background: var(--primary-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: var(--primary-color);
    transition: var(--transition);
}

.contact-info-item:hover i {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

/* 增强页脚样式 */
footer {
    background: linear-gradient(135deg, #222 0%, #333 100%) !important;
    color: #fff;
    position: relative;
    overflow: hidden;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

footer h5, footer h6 {
    color: #fff;
    font-weight: 600;
    margin-bottom: 1.5rem;
    position: relative;
}

footer h5::after, footer h6::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 2px;
    background: var(--accent-color);
}

footer a {
    color: #ccc;
    transition: var(--transition);
    position: relative;
}

footer a:hover {
    color: var(--accent-color);
    text-decoration: none;
    transform: translateX(5px);
}

footer .social-links a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    margin-right: 1rem;
    transition: var(--transition);
}

footer .social-links a:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px) scale(1.1);
}

/* 返回顶部按钮增强 */
#backToTop {
    width: 55px;
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-primary);
    border: none;
    border-radius: 50%;
    box-shadow: var(--shadow-md);
    z-index: 999;
    transition: var(--transition);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
}

#backToTop.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

#backToTop:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow: var(--shadow-lg);
}

#backToTop i {
    font-size: 1.2rem;
    color: white;
}

/* 响应式增强 */
@media (max-width: 767px) {
    .hero-section {
        padding: 100px 0 60px 0;
        min-height: 500px;
    }
    
    .hero-section h1 {
        font-size: 2.5rem;
        line-height: 1.3;
    }
    
    .hero-section .lead {
        font-size: 1.1rem;
    }
    
    .btn {
        padding: 0.6rem 1.5rem;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .icon-box {
        width: 60px;
        height: 60px;
        font-size: 2rem;
    }
    
    .contact-card {
        padding: 2rem;
    }
}

@media (max-width: 575px) {
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .hero-section .lead {
        font-size: 1rem;
    }
    
    .navbar-brand {
        font-size: 1.5rem;
    }
    
    .card-body {
        padding: 1.25rem;
    }
}

/* 新增动画效果 */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.bounce {
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* 滚动指示器 */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--primary-light);
    z-index: 9999;
}

.scroll-progress {
    height: 100%;
    background: var(--gradient-primary);
    width: 0%;
    transition: width 0.1s ease;
}

/* 加载优化 */
.lazy-load {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.lazy-load.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000080;
        --primary-dark: #000060;
        --shadow-sm: 0 2px 8px rgba(0,0,0,0.3);
        --shadow-md: 0 4px 16px rgba(0,0,0,0.4);
        --shadow-lg: 0 8px 32px rgba(0,0,0,0.5);
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #1a1a1a;
        --dark-color: #f0f0f0;
    }
    
    .card {
        background: #2d2d2d;
        color: #f0f0f0;
    }
    
    .navbar {
        background: rgba(0,0,0,0.9) !important;
    }
    
    .form-control, .form-select {
        background: #2d2d2d;
        border-color: #444;
        color: #f0f0f0;
    }
}

/* 英文版样式调整 */
html[lang="en"] body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 1s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 产品详情页样式 */
.product-detail-img {
    height: 400px;
    object-fit: cover;
}

.product-specs {
    list-style: none;
    padding-left: 0;
}

.product-specs li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

/* 在线商城样式 */
.shop-filters {
    background-color: #f8f9fa;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.product-price {
    font-weight: bold;
    color: var(--primary-color);
}

.product-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 0.25rem 0.75rem;
    background-color: var(--accent-color);
    color: #212529;
    font-weight: 500;
    font-size: 0.8rem;
}

/* 定制服务页面样式 */
.custom-service-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.process-step {
    position: relative;
    padding-left: 60px;
    margin-bottom: 2rem;
}

.process-step-number {
    position: absolute;
    left: 0;
    top: 0;
    width: 45px;
    height: 45px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* 成功案例页面样式 */
.case-study-card {
    position: relative;
    overflow: hidden;
}

.case-study-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 1.5rem;
    transform: translateY(100%);
    transition: all 0.3s ease;
}

.case-study-card:hover .case-study-overlay {
    transform: translateY(0);
}

.case-study-img {
    height: 300px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.case-study-card:hover .case-study-img {
    transform: scale(1.05);
}

/* 关于我们页面样式 */
.about-img {
    height: 400px;
    object-fit: cover;
}

.timeline {
    position: relative;
    padding-left: 50px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 20px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--primary-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -50px;
    top: 5px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: var(--primary-color);
}

.team-member-img {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 50%;
    margin-bottom: 1rem;
}

/* 联系我们页面样式 */
.contact-info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
}

.contact-info-icon {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-right: 1rem;
    flex-shrink: 0;
}

.map-container {
    height: 400px;
}

/* 合作伙伴卡片商务风格 */
.partner-card {
    background: #fff;
    border-radius: 1rem;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    transition: transform 0.2s cubic-bezier(.4,2,.6,1), box-shadow 0.2s;
    min-height: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.partner-card:hover {
    transform: translateY(-6px) scale(1.03);
    box-shadow: 0 8px 24px rgba(0,86,179,0.10);
}
.partner-logo {
    max-height: 48px;
    max-width: 100%;
    object-fit: contain;
    filter: grayscale(10%) contrast(1.1);
    transition: filter 0.2s;
}
.partner-card:hover .partner-logo {
    filter: none;
}
