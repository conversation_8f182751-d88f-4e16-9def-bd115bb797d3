<template>
  <div class="card h-100 border-0 shadow-sm">
    <div class="card-body text-center p-4">
      <div class="icon-box mb-3">
        <i :class="`bi bi-${service.icon} text-primary fs-1`"></i>
      </div>
      <h4>{{ service.title }}</h4>
      <p class="text-muted">{{ service.description }}</p>
    </div>
  </div>
</template>

<script setup>
defineProps({
  service: {
    type: Object,
    required: true
  }
})
</script>