# 晨兴印刷Vue3网站启动脚本
Write-Host "正在启动晨兴印刷Vue3网站..." -ForegroundColor Green
Write-Host ""
Write-Host "请确保已安装Node.js (版本 16 或更高)" -ForegroundColor Yellow
Write-Host ""

# 检查Node.js是否安装
try {
    $nodeVersion = node --version
    Write-Host "检测到Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "错误: 未检测到Node.js，请先安装Node.js" -ForegroundColor Red
    Write-Host "下载地址: https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

# 检查是否已安装依赖
if (-not (Test-Path "node_modules")) {
    Write-Host "正在安装项目依赖..." -ForegroundColor Yellow
    npm install
    Write-Host ""
}

Write-Host "启动开发服务器..." -ForegroundColor Green
Write-Host "网站将在 http://localhost:5173 打开" -ForegroundColor Cyan
Write-Host ""
Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Yellow
Write-Host ""

# 启动Vite开发服务器
try {
    npx vite --host
} catch {
    Write-Host "启动失败，请检查错误信息" -ForegroundColor Red
    Read-Host "按任意键退出"
}
