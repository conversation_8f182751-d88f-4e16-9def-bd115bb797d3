// 等待文档加载完成
document.addEventListener('DOMContentLoaded', function() {
    // 导航栏滚动效果
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                navbar.classList.add('shadow-sm');
                navbar.style.padding = '0.5rem 0';
            } else {
                navbar.classList.remove('shadow-sm');
                navbar.style.padding = '1rem 0';
            }
        });
    }

    // 返回顶部按钮
    const backToTopBtn = document.getElementById('backToTop');
    if (backToTopBtn) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 300) {
                backToTopBtn.classList.remove('d-none');
            } else {
                backToTopBtn.classList.add('d-none');
            }
        });

        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // 联系表单验证
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 简单验证示例
            let isValid = true;
            const inputs = contactForm.querySelectorAll('input[required], textarea[required]');
            
            inputs.forEach(input => {
                if (!input.value.trim()) {
                    isValid = false;
                    input.classList.add('is-invalid');
                } else {
                    input.classList.remove('is-invalid');
                }
            });

            if (isValid) {
                // 在实际应用中，这里会发送表单数据到服务器
                alert('您的询价已提交，我们将尽快与您联系！');
                contactForm.reset();
            }
        });
    }

    // 产品图片点击放大效果
    const productImages = document.querySelectorAll('.product-card img');
    productImages.forEach(img => {
        img.addEventListener('click', function() {
            const modal = document.createElement('div');
            modal.classList.add('modal', 'fade', 'show');
            modal.style.display = 'block';
            modal.style.backgroundColor = 'rgba(0,0,0,0.8)';
            modal.style.zIndex = '1050';
            modal.style.position = 'fixed';
            modal.style.top = '0';
            modal.style.left = '0';
            modal.style.width = '100%';
            modal.style.height = '100%';
            modal.style.overflow = 'hidden';
            modal.style.outline = '0';
            
            const modalDialog = document.createElement('div');
            modalDialog.classList.add('modal-dialog', 'modal-dialog-centered', 'modal-xl');
            modalDialog.style.maxWidth = '90%';
            
            const modalContent = document.createElement('div');
            modalContent.classList.add('modal-content', 'bg-transparent', 'border-0');
            
            const modalBody = document.createElement('div');
            modalBody.classList.add('modal-body', 'text-center', 'p-0');
            
            const image = document.createElement('img');
            image.src = this.src;
            image.classList.add('img-fluid');
            image.style.maxHeight = '90vh';
            
            const closeBtn = document.createElement('button');
            closeBtn.type = 'button';
            closeBtn.classList.add('btn-close', 'btn-close-white', 'position-absolute');
            closeBtn.style.top = '20px';
            closeBtn.style.right = '20px';
            closeBtn.setAttribute('aria-label', 'Close');
            
            closeBtn.addEventListener('click', function() {
                document.body.removeChild(modal);
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
            });
            
            modalBody.appendChild(image);
            modalContent.appendChild(modalBody);
            modalContent.appendChild(closeBtn);
            modalDialog.appendChild(modalContent);
            modal.appendChild(modalDialog);
            
            document.body.appendChild(modal);
            document.body.classList.add('modal-open');
            document.body.style.overflow = 'hidden';
            document.body.style.paddingRight = '17px';
            
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';
                }
            });
        });
    });

    // 语言切换功能
    const languageToggle = document.querySelector('a[href="en/index.html"]');
    if (languageToggle) {
        languageToggle.addEventListener('click', function(e) {
            // 在实际应用中，这里会切换到英文版页面
            // 这里仅作为示例
            e.preventDefault();
            const currentPath = window.location.pathname;
            const isEnglish = currentPath.includes('/en/');
            
            if (isEnglish) {
                // 如果当前是英文版，切换到中文版
                window.location.href = currentPath.replace('/en/', '/');
            } else {
                // 如果当前是中文版，切换到英文版
                const newPath = currentPath.replace(/\/([^\/]+)$/, '/en/$1');
                window.location.href = newPath;
            }
        });
    }

    // 购物车功能
    const addToCartButtons = document.querySelectorAll('.add-to-cart');
    if (addToCartButtons.length > 0) {
        let cart = JSON.parse(localStorage.getItem('cart')) || [];
        
        // 更新购物车计数
        function updateCartCount() {
            const cartCountElement = document.getElementById('cartCount');
            if (cartCountElement) {
                cartCountElement.textContent = cart.reduce((total, item) => total + item.quantity, 0);
            }
        }
        
        updateCartCount();
        
        addToCartButtons.forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.getAttribute('data-product-id');
                const productName = this.getAttribute('data-product-name');
                const productPrice = parseFloat(this.getAttribute('data-product-price'));
                const productImage = this.getAttribute('data-product-image');
                
                // 检查产品是否已在购物车中
                const existingItemIndex = cart.findIndex(item => item.id === productId);
                
                if (existingItemIndex > -1) {
                    // 如果产品已在购物车中，增加数量
                    cart[existingItemIndex].quantity += 1;
                } else {
                    // 如果产品不在购物车中，添加新项目
                    cart.push({
                        id: productId,
                        name: productName,
                        price: productPrice,
                        image: productImage,
                        quantity: 1
                    });
                }
                
                // 保存到本地存储
                localStorage.setItem('cart', JSON.stringify(cart));
                
                // 更新购物车计数
                updateCartCount();
                
                // 显示添加成功消息
                alert(`${productName} 已添加到购物车`);
            });
        });
    }

    // 产品筛选功能
    const filterButtons = document.querySelectorAll('.filter-btn');
    if (filterButtons.length > 0) {
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const filterValue = this.getAttribute('data-filter');
                
                // 移除所有按钮的active类
                filterButtons.forEach(btn => btn.classList.remove('active'));
                
                // 添加active类到当前按钮
                this.classList.add('active');
                
                const productItems = document.querySelectorAll('.product-item');
                
                productItems.forEach(item => {
                    if (filterValue === 'all') {
                        item.style.display = 'block';
                    } else {
                        if (item.classList.contains(filterValue)) {
                            item.style.display = 'block';
                        } else {
                            item.style.display = 'none';
                        }
                    }
                });
            });
        });
    }

    // 图片延迟加载
    const lazyImages = document.querySelectorAll('img[data-src]');
    if (lazyImages.length > 0) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.getAttribute('data-src');
                    img.removeAttribute('data-src');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        lazyImages.forEach(img => {
            imageObserver.observe(img);
        });
    }

    // 动画效果
    const animatedElements = document.querySelectorAll('.fade-in');
    if (animatedElements.length > 0) {
        const elementObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                    observer.unobserve(entry.target);
                }
            });
        });
        
        animatedElements.forEach(element => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            element.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            elementObserver.observe(element);
        });
    }
});
