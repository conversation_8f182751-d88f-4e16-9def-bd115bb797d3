<template>
  <div>
    <!-- 页面加载器 -->
    <div class="page-loader" id="pageLoader" v-if="isLoading">
      <div class="loader-spinner"></div>
    </div>

    <!-- 滚动进度条 -->
    <div class="scroll-indicator">
      <div class="scroll-progress" :style="{width: scrollProgress + '%'}"></div>
    </div>
    
    <!-- 顶部导航栏 -->
    <Navbar />
    
    <!-- 路由视图 -->
    <router-view v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
    
    <!-- 页脚 -->
    <Footer />

    <!-- 返回顶部按钮 -->
    <button 
      id="backToTop" 
      class="btn btn-success rounded-circle position-fixed top-50 end-0 me-4" 
      :class="{'d-none': !showBackToTop}"
      @click="scrollToTop"
      aria-label="返回页面顶部">
      <i class="bi bi-arrow-up" aria-hidden="true"></i>
      <span class="visually-hidden">返回顶部</span>
    </button>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import Navbar from './components/Navbar.vue'
import Footer from './components/Footer.vue'

const isLoading = ref(true)
const scrollProgress = ref(0)
const showBackToTop = ref(false)

// 页面加载完成后隐藏加载器
onMounted(() => {
  setTimeout(() => {
    isLoading.value = false
  }, 800)
  
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})

// 处理滚动事件
const handleScroll = () => {
  // 计算滚动进度
  const totalHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight
  scrollProgress.value = (window.scrollY / totalHeight) * 100
  
  // 显示/隐藏返回顶部按钮
  showBackToTop.value = window.scrollY > 300
}

// 返回顶部
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}
</script>
