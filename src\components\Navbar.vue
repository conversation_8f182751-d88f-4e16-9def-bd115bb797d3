<template>
  <nav class="navbar navbar-expand-lg navbar-light fixed-top" :class="{'shadow-sm': scrolled}" id="mainNavbar" role="navigation" aria-label="主导航">
    <div class="container">
      <router-link class="navbar-brand" to="/" aria-label="晨兴印刷首页">
        <span class="fw-bold">晨兴印刷</span>
      </router-link>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
              aria-controls="navbarNav" aria-expanded="false" aria-label="切换导航菜单">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav ms-auto">
          <li v-for="item in navItems" :key="item.path" class="nav-item">
            <router-link class="nav-link" :to="item.path" :aria-current="isActive(item.path) ? 'page' : undefined" :class="{ active: isActive(item.path) }">
              {{ item.name }}
            </router-link>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#" @click.prevent="switchLanguage" aria-label="切换语言">
              <i class="bi bi-globe" aria-hidden="true"></i> {{ currentLang === 'zh' ? 'English' : '中文' }}
            </a>
          </li>
        </ul>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const scrolled = ref(false)
const currentLang = ref('zh')

const navItems = [
  { name: '首页', path: '/' },
  { name: '关于我们', path: '/about' },
  { name: '产品展示', path: '/products' },
  { name: '在线商城', path: '/shop' },
  { name: '定制服务', path: '/custom' },
  { name: '成功案例', path: '/cases' },
  { name: '联系我们', path: '/contact' }
]

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

const handleScroll = () => {
  scrolled.value = window.scrollY > 50
}

const isActive = (path) => {
  return route.path === path
}

const switchLanguage = () => {
  currentLang.value = currentLang.value === 'zh' ? 'en' : 'zh'
  // 实际项目中可以使用i18n库处理语言切换
  // 这里简单模拟语言切换
  if (currentLang.value === 'en') {
    router.push('/en' + route.path)
  } else {
    router.push(route.path.replace('/en', ''))
  }
}
</script>