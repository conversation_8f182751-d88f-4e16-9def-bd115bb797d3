<template>
  <div class="card product-card h-100 border-0 shadow-sm">
    <img :src="product.image" class="card-img-top" :alt="product.title" style="height: 200px; object-fit: cover;">
    <div class="card-body">
      <h5 class="card-title">{{ product.title }}</h5>
      <p class="card-text text-muted">{{ product.description }}</p>
      <router-link :to="product.link" class="btn btn-outline-primary">了解更多</router-link>
    </div>
  </div>
</template>

<script setup>
defineProps({
  product: {
    type: Object,
    required: true
  }
})
</script>

<style scoped>
.product-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
}
</style>