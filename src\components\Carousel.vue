<template>
  <div :id="id" class="carousel slide" data-bs-ride="carousel" :data-bs-interval="interval" aria-label="产品展示轮播" aria-roledescription="轮播" aria-live="polite">
    <!-- 轮播指示器 -->
    <div class="carousel-indicators">
      <button 
        v-for="(item, index) in items" 
        :key="index"
        type="button" 
        :data-bs-target="`#${id}`" 
        :data-bs-slide-to="index" 
        :class="{ active: index === 0 }" 
        :aria-current="index === 0 ? 'true' : undefined" 
        :aria-label="`第${index+1}张幻灯片 - ${item.alt}`">
      </button>
    </div>
    <!-- 轮播内容 -->
    <div class="carousel-inner rounded shadow">
      <div 
        v-for="(item, index) in items" 
        :key="index"
        class="carousel-item" 
        :class="{ active: index === 0 }" 
        aria-roledescription="幻灯片" 
        :aria-label="`${index+1}/${items.length} - ${item.label}`">
        <img :src="item.image" class="d-block w-100" :alt="item.alt">
      </div>
    </div>
    <!-- 轮播控制按钮 -->
    <button class="carousel-control-prev" type="button" :data-bs-target="`#${id}`" 
            data-bs-slide="prev" aria-label="上一张图片">
      <span class="carousel-control-prev-icon" aria-hidden="true"></span>
      <span class="visually-hidden">上一张幻灯片</span>
    </button>
    <button class="carousel-control-next" type="button" :data-bs-target="`#${id}`" 
            data-bs-slide="next" aria-label="下一张图片">
      <span class="carousel-control-next-icon" aria-hidden="true"></span>
      <span class="visually-hidden">下一张幻灯片</span>
    </button>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'

// 定义组件属性
const props = defineProps({
  id: {
    type: String,
    required: true
  },
  items: {
    type: Array,
    required: true
  },
  interval: {
    type: Number,
    default: 4000
  }
})

onMounted(() => {
  // 初始化Bootstrap轮播
  if (typeof bootstrap !== 'undefined') {
    new bootstrap.Carousel(document.getElementById(props.id))
  }
})
</script>