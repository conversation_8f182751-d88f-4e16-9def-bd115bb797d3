# 晨兴印刷Vue3网站启动说明

## 🚀 快速启动

### 方法一：简单测试版本（推荐）

1. **使用Python HTTP服务器**：
   ```bash
   python -m http.server 8080
   ```
   然后在浏览器中访问：http://localhost:8080/test.html

2. **使用Node.js HTTP服务器**：
   ```bash
   npx http-server . -p 8080
   ```
   然后在浏览器中访问：http://localhost:8080/test.html

### 方法二：完整Vue开发环境

1. **确保在vue3-website目录中**：
   ```bash
   cd vue3-website
   ```

2. **安装依赖**（首次运行）：
   ```bash
   npm install
   ```

3. **启动开发服务器**：
   ```bash
   npx vite --host
   ```

### 方法三：使用启动脚本

1. **Windows批处理文件**：
   - 双击 `start.bat` 文件

2. **PowerShell脚本**：
   - 右键点击 `start.ps1` → "使用PowerShell运行"

## 🌐 访问网站

启动成功后，在浏览器中访问：
- **本地地址**：http://localhost:5173
- **网络地址**：http://192.168.x.x:5173（局域网其他设备可访问）

## ❗ 常见问题解决

### 1. "找不到Node.js"错误
**解决方案**：
- 下载并安装Node.js：https://nodejs.org/
- 选择LTS版本（推荐版本16或更高）
- 安装完成后重启命令行

### 2. "npm不是内部或外部命令"
**解决方案**：
- 重新安装Node.js
- 确保安装时勾选了"Add to PATH"选项
- 重启电脑

### 3. 端口被占用
**解决方案**：
- 关闭其他占用5173端口的程序
- 或使用其他端口：`npx vite --port 3000`

### 4. 权限错误
**解决方案**：
- 以管理员身份运行命令行
- 或使用PowerShell：`Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`

### 5. 依赖安装失败
**解决方案**：
- 删除 `node_modules` 文件夹
- 删除 `package-lock.json` 文件
- 重新运行 `npm install`

## 🛠️ 开发命令

- **启动开发服务器**：`npm run dev`
- **构建生产版本**：`npm run build`
- **预览生产版本**：`npm run preview`

## 📞 技术支持

如果遇到其他问题，请检查：
1. Node.js版本是否为16或更高
2. 网络连接是否正常
3. 防火墙是否阻止了端口访问

## 🎯 项目特性

- ✅ Vue 3 + Vite
- ✅ 响应式设计
- ✅ 组件化开发
- ✅ 热重载开发
- ✅ 现代化构建工具
