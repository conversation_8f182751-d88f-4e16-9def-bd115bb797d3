# 项目启动指南

## 方法一：使用Vite创建项目（推荐）

1. 创建新项目：
```bash
npm create vue@latest chenxing-printing-vue
```

2. 按照提示选择配置：
   - ✓ Add TypeScript? No
   - ✓ Add JSX Support? No
   - ✓ Add Vue Router for Single Page Application development? Yes
   - ✓ Add Pinia for state management? No (可选)
   - ✓ Add Vitest for Unit testing? No
   - ✓ Add an End-to-End Testing Solution? No
   - ✓ Add ESLint for code quality? Yes (可选)
   - ✓ Add Prettier for code formatting? Yes (可选)

3. 进入项目目录并安装依赖：
```bash
cd chenxing-printing-vue
npm install
```

4. 安装Bootstrap和Bootstrap Icons：
```bash
npm install bootstrap bootstrap-icons
```

5. 将之前提供的Vue组件文件复制到相应目录：
   - 将App.vue放入src目录
   - 将组件文件放入src/components目录
   - 将视图文件放入src/views目录

6. 启动开发服务器：
```bash
npm run dev
```

## 方法二：使用现有项目添加Vue

1. 在现有项目中安装Vue和相关依赖：
```bash
npm install vue vue-router
npm install -D vite @vitejs/plugin-vue
```

2. 创建vite.config.js文件：
```js
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  server: {
    port: 3000
  }
})
```

3. 更新package.json中的脚本：
```json
"scripts": {
  "dev": "vite",
  "build": "vite build",
  "preview": "vite preview"
}
```

4. 创建src/main.js文件：
```js
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import 'bootstrap/dist/css/bootstrap.min.css'
import 'bootstrap-icons/font/bootstrap-icons.css'
import 'bootstrap/dist/js/bootstrap.bundle.min.js'

const app = createApp(App)
app.use(router)
app.mount('#app')
```

5. 创建index.html文件：
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>广州市晨兴印刷有限公司 - 专业印刷服务</title>
</head>
<body>
  <div id="app"></div>
  <script type="module" src="/src/main.js"></script>
</body>
</html>
```

6. 启动开发服务器：
```bash
npm run dev
```
