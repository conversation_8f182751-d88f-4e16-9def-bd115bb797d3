<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购物车 - 广州市晨兴印刷有限公司</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white fixed-top shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <span class="fw-bold text-primary">晨兴印刷</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">关于我们</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.html">产品展示</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="shop.html">在线商城</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="custom.html">定制服务</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="cases.html">成功案例</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">联系我们</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="en/cart.html"><i class="bi bi-globe"></i> English</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="cart.html">
                            <i class="bi bi-cart3 fs-5"></i>
                            <span class="badge bg-primary rounded-pill" id="cartCount">0</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 页面标题 -->
    <div class="bg-light py-5 mt-5">
        <div class="container py-4">
            <h1 class="fw-bold">购物车</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="index.html">首页</a></li>
                    <li class="breadcrumb-item"><a href="shop.html">在线商城</a></li>
                    <li class="breadcrumb-item active" aria-current="page">购物车</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- 购物车主体 -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <!-- 购物车为空提示 -->
                    <div id="emptyCart" class="text-center py-5">
                        <i class="bi bi-cart3 text-muted" style="font-size: 5rem;"></i>
                        <h3 class="mt-4">您的购物车是空的</h3>
                        <p class="text-muted">浏览我们的商品并添加到购物车</p>
                        <a href="shop.html" class="btn btn-primary mt-3">继续购物</a>
                    </div>

                    <!-- 购物车商品列表 -->
                    <div id="cartItems" class="d-none">
                        <div class="table-responsive">
                            <table class="table align-middle">
                                <thead>
                                    <tr>
                                        <th scope="col" colspan="2">商品</th>
                                        <th scope="col">单价</th>
                                        <th scope="col">数量</th>
                                        <th scope="col">小计</th>
                                        <th scope="col"></th>
                                    </tr>
                                </thead>
                                <tbody id="cartItemsList">
                                    <!-- 购物车商品将通过JavaScript动态添加 -->
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <a href="shop.html" class="btn btn-outline-primary">
                                <i class="bi bi-arrow-left me-2"></i>继续购物
                            </a>
                            <button id="clearCart" class="btn btn-outline-danger">
                                <i class="bi bi-trash me-2"></i>清空购物车
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 mt-4 mt-lg-0">
                    <!-- 订单摘要 -->
                    <div id="orderSummary" class="card border-0 shadow-sm d-none">
                        <div class="card-header bg-white py-3">
                            <h5 class="mb-0 fw-bold">订单摘要</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-3">
                                <span>商品小计</span>
                                <span id="subtotal">¥0.00</span>
                            </div>
                            <div class="d-flex justify-content-between mb-3">
                                <span>运费</span>
                                <span id="shipping">¥0.00</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between mb-4 fw-bold">
                                <span>总计</span>
                                <span id="total" class="text-primary">¥0.00</span>
                            </div>

                            <!-- 优惠码 -->
                            <div class="mb-4">
                                <label for="couponCode" class="form-label">优惠码</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="couponCode" placeholder="输入优惠码">
                                    <button class="btn btn-outline-primary" type="button">应用</button>
                                </div>
                            </div>

                            <!-- 结算按钮 -->
                            <button id="checkout" class="btn btn-primary w-100 py-2">
                                前往结算
                            </button>
                        </div>
                    </div>

                    <!-- 客户服务 -->
                    <div class="card border-0 shadow-sm mt-4">
                        <div class="card-body">
                            <h5 class="fw-bold mb-3">需要帮助？</h5>
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="bi bi-telephone text-primary me-2"></i>
                                    <span>客服热线: 020-12345678</span>
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-envelope text-primary me-2"></i>
                                    <span>邮箱: <EMAIL></span>
                                </li>
                                <li>
                                    <i class="bi bi-clock text-primary me-2"></i>
                                    <span>工作时间: 周一至周五 9:00-18:00</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 推荐产品 -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold">您可能还喜欢</h2>
                <p class="text-muted">根据您的浏览历史推荐</p>
            </div>
            <div class="row g-4">
                <div class="col-md-3">
                    <div class="card product-card h-100 border-0 shadow-sm">
                        <span class="product-badge">热销</span>
                        <img src="images/product-catalog-1.jpg" class="card-img-top" alt="高档企业画册">
                        <div class="card-body">
                            <h5 class="card-title">高档企业画册</h5>
                            <p class="card-text text-muted small">精装硬壳，内页采用进口铜版纸，UV工艺</p>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <span class="product-price">¥680.00起</span>
                                <button class="btn btn-primary btn-sm add-to-cart" 
                                        data-product-id="1" 
                                        data-product-name="高档企业画册" 
                                        data-product-price="680" 
                                        data-product-image="images/product-catalog-1.jpg">
                                    <i class="bi bi-cart-plus"></i> 加入购物车
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card product-card h-100 border-0 shadow-sm">
                        <span class="product-badge">热销</span>
                        <img src="images/product-box-1.jpg" class="card-img-top" alt="高端礼品包装盒">
                        <div class="card-body">
                            <h5 class="card-title">高端礼品包装盒</h5>
                            <p class="card-text text-muted small">特种纸材质，烫金工艺，磁铁扣设计</p>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <span class="product-price">¥780.00起</span>
                                <button class="btn btn-primary btn-sm add-to-cart" 
                                        data-product-id="4" 
                                        data-product-name="高端礼品包装盒" 
                                        data-product-price="780" 
                                        data-product-image="images/product-box-1.jpg">
                                    <i class="bi bi-cart-plus"></i> 加入购物车
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card product-card h-100 border-0 shadow-sm">
                        <span class="product-badge">热销</span>
                        <img src="images/product-bag-2.jpg" class="card-img-top" alt="高档礼品纸袋">
                        <div class="card-body">
                            <h5 class="card-title">高档礼品纸袋</h5>
                            <p class="card-text text-muted small">特种纸材质，烫金工艺，丝带手提</p>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <span class="product-price">¥380.00起</span>
                                <button class="btn btn-primary btn-sm add-to-cart" 
                                        data-product-id="8" 
                                        data-product-name="高档礼品纸袋" 
                                        data-product-price="380" 
                                        data-product-image="images/product-bag-2.jpg">
                                    <i class="bi bi-cart-plus"></i> 加入购物车
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card product-card h-100 border-0 shadow-sm">
                        <span class="product-badge">环保</span>
                        <img src="images/product-bag-3.jpg" class="card-img-top" alt="环保购物纸袋">
                        <div class="card-body">
                            <h5 class="card-title">环保购物纸袋</h5>
                            <p class="card-text text-muted small">牛皮纸材质，环保印刷，平纹绳手提</p>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <span class="product-price">¥180.00起</span>
                                <button class="btn btn-primary btn-sm add-to-cart" 
                                        data-product-id="9" 
                                        data-product-name="环保购物纸袋" 
                                        data-product-price="180" 
                                        data-product-image="images/product-bag-3.jpg">
                                    <i class="bi bi-cart-plus"></i> 加入购物车
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row g-4">
                <div class="col-md-4">
                    <h5 class="fw-bold mb-3">广州市晨兴印刷有限公司</h5>
                    <p class="small">专注于高品质印刷服务，为客户提供画册、包装盒、纸袋等全方位印刷解决方案。</p>
                </div>
                <div class="col-md-2">
                    <h6 class="fw-bold mb-3">快速链接</h6>
                    <ul class="list-unstyled small">
                        <li class="mb-2"><a href="index.html" class="text-white text-decoration-none">首页</a></li>
                        <li class="mb-2"><a href="about.html" class="text-white text-decoration-none">关于我们</a></li>
                        <li class="mb-2"><a href="products.html" class="text-white text-decoration-none">产品展示</a></li>
                        <li class="mb-2"><a href="contact.html" class="text-white text-decoration-none">联系我们</a></li>
                    </ul>
                </div>
                <div class="col-md-2">
                    <h6 class="fw-bold mb-3">产品服务</h6>
                    <ul class="list-unstyled small">
                        <li class="mb-2"><a href="products.html" class="text-white text-decoration-none">画册印刷</a></li>
                        <li class="mb-2"><a href="products.html" class="text-white text-decoration-none">包装盒制作</a></li>
                        <li class="mb-2"><a href="products.html" class="text-white text-decoration-none">纸袋制作</a></li>
                        <li class="mb-2"><a href="custom.html" class="text-white text-decoration-none">定制服务</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h6 class="fw-bold mb-3">订阅我们</h6>
                    <p class="small">订阅我们的电子邮件，获取最新产品和优惠信息</p>
                    <div class="input-group mb-3">
                        <input type="email" class="form-control" placeholder="您的邮箱地址">
                        <button class="btn btn-primary" type="button">订阅</button>
                    </div>
                </div>
            </div>
            <hr class="my-4 bg-secondary">
            <div class="row">
                <div class="col-md-6 small">
                    <p class="mb-0">&copy; 2025 广州市晨兴印刷有限公司. 保留所有权利.</p>
                </div>
                <div class="col-md-6 text-md-end small">
                    <a href="#" class="text-white text-decoration-none me-3">隐私政策</a>
                    <a href="#" class="text-white text-decoration-none">使用条款</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- 返回顶部按钮 -->
    <button id="backToTop" class="btn btn-primary rounded-circle position-fixed bottom-0 end-0 m-4 d-none">
        <i class="bi bi-arrow-up"></i>
    </button>

    <!-- 购物车项目模板 -->
    <template id="cartItemTemplate">
        <tr class="cart-item">
            <td style="width: 100px;">
                <img src="" alt="" class="img-fluid rounded cart-item-image">
            </td>
            <td>
                <h6 class="cart-item-name mb-1"></h6>
                <small class="text-muted d-block cart-item-details"></small>
            </td>
            <td class="cart-item-price"></td>
            <td>
                <div class="input-group input-group-sm quantity-control" style="width: 120px;">
                    <button class="btn btn-outline-secondary decrease-quantity" type="button">-</button>
                    <input type="text" class="form-control text-center item-quantity" value="1" readonly>
                    <button class="btn btn-outline-secondary increase-quantity" type="button">+</button>
                </div>
            </td>
            <td class="cart-item-subtotal fw-bold"></td>
            <td>
                <button class="btn btn-sm text-danger remove-item">
                    <i class="bi bi-x-lg"></i>
                </button>
            </td>
        </tr>
    </template>

    <!-- JavaScript 文件 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 从localStorage获取购物车数据
            function getCartItems() {
                return JSON.parse(localStorage.getItem('cartItems')) || [];
            }

            // 保存购物车数据到localStorage
            function saveCartItems(items) {
                localStorage.setItem('cartItems', JSON.stringify(items));
            }

            // 更新购物车计数
            function updateCartCount() {
                const cartItems = getCartItems();
                const count = cartItems.reduce((total, item) => total + item.quantity, 0);
                document.getElementById('cartCount').textContent = count;
                return count;
            }

            // 计算购物车总价
            function calculateTotal() {
                const cartItems = getCartItems();
                const subtotal = cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
                
                // 设置运费规则（示例：订单满1000免运费，否则收取50元运费）
                const shipping = subtotal >= 1000 ? 0 : 50;
                
                const total = subtotal + shipping;
                
                // 更新页面显示
                document.getElementById('subtotal').textContent = `¥${subtotal.toFixed(2)}`;
                document.getElementById('shipping').textContent = shipping === 0 ? '免运费' : `¥${shipping.toFixed(2)}`;
                document.getElementById('total').textContent = `¥${total.toFixed(2)}`;
            }

            // 渲染购物车项目
            function renderCartItems() {
                const cartItems = getCartItems();
                const cartItemsList = document.getElementById('cartItemsList');
                const template = document.getElementById('cartItemTemplate');
                
                // 清空现有项目
                cartItemsList.innerHTML = '';
                
                // 添加每个购物车项目
                cartItems.forEach(item => {
                    const clone = template.content.cloneNode(true);
                    
                    // 设置项目数据
                    clone.querySelector('.cart-item-image').src = item.image;
                    clone.querySelector('.cart-item-image').alt = item.name;
                    clone.querySelector('.cart-item-name').textContent = item.name;
                    clone.querySelector('.cart-item-details').textContent = `最小起订量: ${item.minOrder || '50个'}`;
                    clone.querySelector('.cart-item-price').textContent = `¥${item.price.toFixed(2)}`;
                    clone.querySelector('.item-quantity').value = item.quantity;
                    clone.querySelector('.cart-item-subtotal').textContent = `¥${(item.price * item.quantity).toFixed(2)}`;
                    
                    // 设置数据属性
                    const cartItemRow = clone.querySelector('.cart-item');
                    cartItemRow.dataset.productId = item.id;
                    
                    // 添加事件监听器
                    clone.querySelector('.decrease-quantity').addEventListener('click', function() {
                        updateItemQuantity(item.id, item.quantity - 1);
                    });
                    
                    clone.querySelector('.increase-quantity').addEventListener('click', function() {
                        updateItemQuantity(item.id, item.quantity + 1);
                    });
                    
                    clone.querySelector('.remove-item').addEventListener('click', function() {
                        removeCartItem(item.id);
                    });
                    
                    cartItemsList.appendChild(clone);
                });
                
                // 更新购物车显示状态
                updateCartDisplay();
            }

            // 更新购物车项目数量
            function updateItemQuantity(productId, newQuantity) {
                if (newQuantity < 1) return;
                
                const cartItems = getCartItems();
                const itemIndex = cartItems.findIndex(item => item.id === productId);
                
                if (itemIndex !== -1) {
                    cartItems[itemIndex].quantity = newQuantity;
                    saveCartItems(cartItems);
                    renderCartItems();
                    calculateTotal();
                    updateCartCount();
                }
            }

            // 从购物车移除项目
            function removeCartItem(productId) {
                const cartItems = getCartItems();
                const updatedItems = cartItems.filter(item => item.id !== productId);
                saveCartItems(updatedItems);
                renderCartItems();
                calculateTotal();
                updateCartCount();
            }

            // 清空购物车
            function clearCart() {
                if (confirm('确定要清空购物车吗？')) {
                    saveCartItems([]);
                    renderCartItems();
                    calculateTotal();
                    updateCartCount();
                }
            }

            // 更新购物车显示状态
            function updateCartDisplay() {
                const count = updateCartCount();
                const emptyCart = document.getElementById('emptyCart');
                const cartItems = document.getElementById('cartItems');
                const orderSummary = document.getElementById('orderSummary');
                
                if (count > 0) {
                    emptyCart.classList.add('d-none');
                    cartItems.classList.remove('d-none');
                    orderSummary.classList.remove('d-none');
                } else {
                    emptyCart.classList.remove('d-none');
                    cartItems.classList.add('d-none');
                    orderSummary.classList.add('d-none');
                }
            }

            // 初始化购物车
            renderCartItems();
            calculateTotal();
            
            // 添加事件监听器
            document.getElementById('clearCart').addEventListener('click', clearCart);
            
            document.getElementById('checkout').addEventListener('click', function() {
                alert('结算功能正在开发中，敬请期待！');
            });
            
            // 添加到购物车按钮（用于推荐产品部分）
            document.querySelectorAll('.add-to-cart').forEach(button => {
                button.addEventListener('click', function() {
                    const productId = this.dataset.productId;
                    const productName = this.dataset.productName;
                    const productPrice = parseFloat(this.dataset.productPrice);
                    const productImage = this.dataset.productImage;
                    
                    const cartItems = getCartItems();
                    const existingItem = cartItems.find(item => item.id === productId);
                    
                    if (existingItem) {
                        existingItem.quantity += 1;
                    } else {
                        cartItems.push({
                            id: productId,
                            name: productName,
                            price: productPrice,
                            image: productImage,
                            quantity: 1
                        });
                    }
                    
                    saveCartItems(cartItems);
                    renderCartItems();
                    calculateTotal();
                    updateCartCount();
                    
                    alert(`已将 ${productName} 添加到购物车！`);
                });
            });
        });
    </script>
</body>
</html>
