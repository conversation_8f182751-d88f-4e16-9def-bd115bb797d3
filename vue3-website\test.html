<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>晨兴印刷Vue3网站测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        /* 基本样式 */
        :root {
            --primary-color: #0056b3;
            --primary-light: #e7f3ff;
            --gradient-primary: linear-gradient(135deg, #0056b3, #007bff);
        }
        
        .hero-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
            padding: 120px 0 80px 0;
            min-height: 600px;
        }
        
        .navbar {
            background: rgba(255,255,255,0.95) !important;
            backdrop-filter: blur(10px);
        }
        
        .btn-primary {
            background: var(--gradient-primary);
            border: none;
        }
        
        .card:hover {
            transform: translateY(-5px);
            transition: transform 0.3s ease;
        }
        
        .icon-box {
            width: 80px;
            height: 80px;
            background: var(--primary-light);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-light fixed-top">
            <div class="container">
                <a class="navbar-brand fw-bold" href="#">晨兴印刷</a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="#hero">首页</a>
                    <a class="nav-link" href="#services">服务</a>
                    <a class="nav-link" href="#products">产品</a>
                    <a class="nav-link" href="#contact">联系</a>
                </div>
            </div>
        </nav>

        <!-- 主横幅 -->
        <section class="hero-section d-flex align-items-center" id="hero">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h1 class="fw-bold mb-4">专业印刷服务，<br>品质成就未来</h1>
                        <p class="lead mb-4">广州市晨兴印刷有限公司专注于高品质画册、包装盒、纸袋等印刷产品</p>
                        <div>
                            <button class="btn btn-primary me-3">
                                <i class="bi bi-arrow-right me-2"></i>浏览产品
                            </button>
                            <button class="btn btn-outline-primary">
                                <i class="bi bi-telephone me-2"></i>联系我们
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="text-center">
                            <img src="public/images/轮播图/印刷机特写.jpg" class="img-fluid rounded shadow" alt="印刷机特写" style="max-height: 400px;">
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 服务介绍 -->
        <section class="py-5 bg-light" id="services">
            <div class="container">
                <div class="text-center mb-5">
                    <h2 class="fw-bold">我们的服务</h2>
                    <p class="text-muted">为您提供全方位的印刷解决方案</p>
                </div>
                <div class="row g-4">
                    <div v-for="service in services" :key="service.title" class="col-md-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center p-4">
                                <div class="icon-box">
                                    <i :class="service.icon + ' text-primary fs-1'"></i>
                                </div>
                                <h4>{{ service.title }}</h4>
                                <p class="text-muted">{{ service.description }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系表单 -->
        <section class="py-5" id="contact">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card border-0 shadow">
                            <div class="card-body p-4">
                                <h3 class="fw-bold mb-4">联系我们获取定制报价</h3>
                                <form @submit.prevent="submitForm">
                                    <div class="mb-3">
                                        <input v-model="form.name" type="text" class="form-control" placeholder="您的姓名" required>
                                    </div>
                                    <div class="mb-3">
                                        <input v-model="form.email" type="email" class="form-control" placeholder="您的邮箱" required>
                                    </div>
                                    <div class="mb-3">
                                        <textarea v-model="form.message" class="form-control" rows="3" placeholder="您的需求描述"></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">提交询价</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 页脚 -->
        <footer class="bg-dark text-white py-4">
            <div class="container">
                <div class="text-center">
                    <h5 class="fw-bold mb-3">广州市晨兴印刷有限公司</h5>
                    <p>&copy; {{ currentYear }} 广州市晨兴印刷有限公司. 保留所有权利.</p>
                </div>
            </div>
        </footer>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const { createApp } = Vue

        createApp({
            data() {
                return {
                    services: [
                        {
                            icon: 'bi bi-book',
                            title: '画册印刷',
                            description: '高品质企业画册、产品目录、宣传册等，展现您的品牌形象'
                        },
                        {
                            icon: 'bi bi-box',
                            title: '包装盒制作',
                            description: '精美产品包装盒设计与制作，提升产品价值与品牌形象'
                        },
                        {
                            icon: 'bi bi-bag',
                            title: '纸袋制作',
                            description: '环保时尚的纸袋设计与制作，满足各类商业与礼品需求'
                        }
                    ],
                    form: {
                        name: '',
                        email: '',
                        message: ''
                    }
                }
            },
            computed: {
                currentYear() {
                    return new Date().getFullYear()
                }
            },
            methods: {
                submitForm() {
                    alert('您的询价已提交，我们将尽快与您联系！')
                    this.form = { name: '', email: '', message: '' }
                }
            }
        }).mount('#app')
    </script>
</body>
</html>
