#!/usr/bin/env node

// 简单的开发服务器启动脚本
const { createServer } = require('vite')

async function startServer() {
  try {
    console.log('正在启动Vue 3开发服务器...')
    
    const server = await createServer({
      server: {
        port: 5173,
        host: true,
        open: true
      }
    })
    
    await server.listen()
    
    console.log('✅ 服务器启动成功!')
    console.log('🌐 本地地址: http://localhost:5173')
    console.log('🌐 网络地址: http://192.168.x.x:5173')
    console.log('⏹️  按 Ctrl+C 停止服务器')
    
  } catch (error) {
    console.error('❌ 启动失败:', error.message)
    process.exit(1)
  }
}

startServer()
